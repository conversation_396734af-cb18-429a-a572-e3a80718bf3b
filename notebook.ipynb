# Import necessary packages for data analysis and visualization
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from thefuzz import process
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# Set visualization style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 11

print("📊 Libraries imported successfully!")
print(f"📈 Pandas version: {pd.__version__}")
print(f"🔢 NumPy version: {np.__version__}")

# Load datasets from multiple file formats
print("🔄 Loading datasets from multiple formats...")

# Load price data (CSV)
prices = pd.read_csv('data/airbnb_price.csv')
print(f"✅ Price data loaded: {prices.shape[0]:,} records")

# Load review data (TSV)
reviews = pd.read_csv('data/airbnb_last_review.tsv', sep='\t')
print(f"✅ Review data loaded: {reviews.shape[0]:,} records")

# Load room type data (Excel)
room_types = pd.read_excel('data/airbnb_room_type.xlsx')
print(f"✅ Room type data loaded: {room_types.shape[0]:,} records")

print("\n📋 Dataset Overview:")
print(f"Total unique listings across datasets: {len(set(prices['listing_id']) | set(reviews['listing_id']) | set(room_types['listing_id'])):,}")

# Merge datasets using outer joins to preserve all records
print("🔗 Merging datasets...")

# Start with prices as base dataset
df = pd.merge(prices, reviews, on='listing_id', how='outer')
print(f"After merging prices + reviews: {df.shape[0]:,} records")

# Add room types
df = pd.merge(df, room_types, on='listing_id', how='outer')
print(f"After merging all datasets: {df.shape[0]:,} records")

# Save integrated dataset
df.to_csv('data/airbnb.csv', index=False)
print("💾 Integrated dataset saved to 'data/airbnb.csv'")

# Display basic info
print("\n📊 Dataset Structure:")
print(df.info())

# Data Quality Assessment
print("🔍 Data Quality Assessment")
print("=" * 50)

# Missing values analysis
missing_data = df.isnull().sum()
missing_percent = (missing_data / len(df)) * 100

quality_df = pd.DataFrame({
    'Missing Count': missing_data,
    'Missing Percentage': missing_percent.round(2)
})

print("\n📋 Missing Data Summary:")
print(quality_df[quality_df['Missing Count'] > 0])

# Display first few rows
print("\n👀 Sample Data:")
print(df.head())

# Basic data cleaning
print("🧹 Starting data cleaning process...")

# Convert date column
df['last_review'] = pd.to_datetime(df['last_review'])
print("✅ Converted 'last_review' to datetime")

# Clean price column (remove 'dollars' suffix and convert to numeric)
df['price'] = df['price'].str.strip("dollars").astype("int")
print("✅ Cleaned and converted 'price' to numeric")

# Extract borough information from nbhood_full
df['borough'] = df['nbhood_full'].str.split(',').str[0]
df['neighborhood'] = df['nbhood_full'].str.split(',').str[1].str.strip()
print("✅ Extracted borough and neighborhood information")

print("\n📊 Updated Dataset Info:")
print(df.info())

# Fuzzy String Matching for Room Types
print("🔤 Implementing Fuzzy String Matching for Room Types")
print("=" * 60)

# Check current room type values
print("\n📋 Original room type values:")
print(df['room_type'].value_counts())

# Define standard categories
standard_categories = ['shared rooms', 'private room', 'entire homes/apartments']
print(f"\n🎯 Target categories: {standard_categories}")

# Apply fuzzy matching
print("\n🔄 Applying fuzzy string matching...")
for standard_category in standard_categories:
    # Find all potential matches for this category
    matches = process.extract(standard_category, df['room_type'], limit=df.shape[0])
    
    # Apply matches with similarity score > 80
    for potential_match in matches:
        if potential_match[1] > 80:
            df.loc[df['room_type'] == potential_match[0], 'room_type'] = standard_category
            
# Convert to categorical for better performance
df['room_type'] = df['room_type'].astype('category')

print("\n✅ Fuzzy matching completed!")
print("\n📊 Cleaned room type distribution:")
print(df['room_type'].value_counts())

# Calculate earliest and most recent review dates
first_reviewed = df['last_review'].min()
last_reviewed = df['last_review'].max()

print("📅 TEMPORAL ANALYSIS RESULTS")
print("=" * 40)
print(f"🗓️ First review date: {first_reviewed.strftime('%Y-%m-%d')}")
print(f"🗓️ Last review date: {last_reviewed.strftime('%Y-%m-%d')}")
print(f"📊 Date range span: {(last_reviewed - first_reviewed).days} days")

# Additional temporal insights
review_data = df.dropna(subset=['last_review'])
print(f"\n📋 Review Activity Summary:")
print(f"   • Total listings with reviews: {len(review_data):,}")
print(f"   • Listings without reviews: {len(df) - len(review_data):,}")
print(f"   • Review coverage: {(len(review_data)/len(df)*100):.1f}%")

# Count private room listings
nb_private_rooms = df['room_type'].value_counts()['private room']

print("🏠 PRIVATE ROOM ANALYSIS")
print("=" * 30)
print(f"🛏️  Private rooms count: {nb_private_rooms:,}")

# Enhanced room type analysis
room_distribution = df['room_type'].value_counts()
room_percentages = df['room_type'].value_counts(normalize=True) * 100

print(f"\n📊 Complete Room Type Distribution:")
for room_type in room_distribution.index:
    count = room_distribution[room_type]
    percentage = room_percentages[room_type]
    print(f"   • {room_type}: {count:,} ({percentage:.1f}%)")

print(f"\n🎯 Private rooms represent {room_percentages['private room']:.1f}% of all listings")

# Calculate average listing price
avg_price = round(df['price'].mean(), 2)

print("💰 PRICING ANALYSIS")
print("=" * 25)
print(f"💵 Average listing price: ${avg_price}")

# Enhanced pricing statistics
price_stats = df['price'].describe()
print(f"\n📊 Comprehensive Price Statistics:")
print(f"   • Minimum price: ${price_stats['min']:.0f}")
print(f"   • 25th percentile: ${price_stats['25%']:.0f}")
print(f"   • Median price: ${price_stats['50%']:.0f}")
print(f"   • Mean price: ${price_stats['mean']:.2f}")
print(f"   • 75th percentile: ${price_stats['75%']:.0f}")
print(f"   • Maximum price: ${price_stats['max']:.0f}")
print(f"   • Standard deviation: ${price_stats['std']:.2f}")

# Price by room type
print(f"\n🏠 Average Price by Room Type:")
price_by_room = df.groupby('room_type')['price'].agg(['mean', 'median', 'count']).round(2)
for room_type in price_by_room.index:
    mean_price = price_by_room.loc[room_type, 'mean']
    median_price = price_by_room.loc[room_type, 'median']
    count = price_by_room.loc[room_type, 'count']
    print(f"   • {room_type}: ${mean_price} (median: ${median_price}, n={count:,})")

# Create the required summary DataFrame
review_dates = pd.DataFrame({
    "first_reviewed": [first_reviewed], 
    "last_reviewed": [last_reviewed], 
    "nb_private_rooms": [nb_private_rooms], 
    "avg_price": [avg_price]
})

print("📋 SUMMARY DATAFRAME - CORE RESULTS")
print("=" * 45)
print(review_dates)

# Enhanced summary with additional metrics
enhanced_summary = pd.DataFrame({
    "Metric": [
        "First Review Date",
        "Last Review Date", 
        "Total Listings",
        "Private Rooms",
        "Entire Homes/Apartments",
        "Shared Rooms",
        "Average Price ($)",
        "Median Price ($)",
        "Price Range ($)"
    ],
    "Value": [
        first_reviewed.strftime('%Y-%m-%d'),
        last_reviewed.strftime('%Y-%m-%d'),
        f"{len(df):,}",
        f"{room_distribution['private room']:,}",
        f"{room_distribution['entire homes/apartments']:,}",
        f"{room_distribution['shared rooms']:,}",
        f"{avg_price:.2f}",
        f"{df['price'].median():.0f}",
        f"{df['price'].min():.0f} - {df['price'].max():.0f}"
    ]
})

print("\n📊 MARKET SUMMARY")
print("=" * 35)
print(enhanced_summary.to_string(index=False))

# Create comprehensive price distribution visualizations
fig, axes = plt.subplots(2, 2, figsize=(16, 12))

# 1. Overall price distribution (with outlier handling)
# Remove extreme outliers for better visualization
price_q99 = df['price'].quantile(0.99)
price_filtered = df[df['price'] <= price_q99]['price']

axes[0,0].hist(price_filtered, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
axes[0,0].axvline(avg_price, color='red', linestyle='--', linewidth=2, label=f'Mean: ${avg_price}')
axes[0,0].axvline(df['price'].median(), color='orange', linestyle='--', linewidth=2, label=f'Median: ${df["price"].median():.0f}')
axes[0,0].set_title('Price Distribution (99th percentile cap)')
axes[0,0].set_xlabel('Price ($)')
axes[0,0].set_ylabel('Frequency')
axes[0,0].legend()
axes[0,0].grid(True, alpha=0.3)

# 2. Price by room type (box plot)
df.boxplot(column='price', by='room_type', ax=axes[0,1])
axes[0,1].set_title('Price Distribution by Room Type')
axes[0,1].set_xlabel('Room Type')
axes[0,1].set_ylabel('Price ($)')
axes[0,1].tick_params(axis='x', rotation=45)

# 3. Price by borough
borough_prices = df.groupby('borough')['price'].mean().sort_values(ascending=False)
borough_prices.plot(kind='bar', ax=axes[1,0], color='lightcoral')
axes[1,0].set_title('Average Price by Borough')
axes[1,0].set_xlabel('Borough')
axes[1,0].set_ylabel('Average Price ($)')
axes[1,0].tick_params(axis='x', rotation=45)
axes[1,0].grid(True, alpha=0.3)

# 4. Room type distribution (pie chart)
room_counts = df['room_type'].value_counts()
colors = ['lightblue', 'lightgreen', 'lightcoral']
axes[1,1].pie(room_counts.values, labels=room_counts.index, autopct='%1.1f%%', 
              colors=colors, startangle=90)
axes[1,1].set_title('Room Type Distribution')

fig.suptitle('NYC Airbnb Price Distribution', fontsize=16, fontweight='bold')
plt.tight_layout()
plt.show()

# Print key insights
print("💡 KEY PRICE INSIGHTS:")
print(f"   • Most expensive borough: {borough_prices.index[0]} (${borough_prices.iloc[0]:.0f} avg)")
print(f"   • Least expensive borough: {borough_prices.index[-1]} (${borough_prices.iloc[-1]:.0f} avg)")
print(f"   • Price range across boroughs: ${borough_prices.iloc[-1]:.0f} - ${borough_prices.iloc[0]:.0f}")

# Geographic analysis by borough
borough_analysis = df.groupby('borough').agg({
    'listing_id': 'count',
    'price': ['mean', 'median', 'std'],
    'last_review': 'count'
}).round(2)

# Flatten column names
borough_analysis.columns = ['Total_Listings', 'Avg_Price', 'Median_Price', 'Price_StdDev', 'Listings_with_Reviews']
borough_analysis['Market_Share_%'] = (borough_analysis['Total_Listings'] / len(df) * 100).round(1)
borough_analysis['Review_Rate_%'] = (borough_analysis['Listings_with_Reviews'] / borough_analysis['Total_Listings'] * 100).round(1)

# Sort by total listings
borough_analysis = borough_analysis.sort_values('Total_Listings', ascending=False)

print("🗺️ GEOGRAPHIC MARKET ANALYSIS")
print("=" * 50)
print(borough_analysis)

# Visualization
fig, axes = plt.subplots(2, 2, figsize=(16, 12))

# 1. Market share by borough
borough_analysis['Market_Share_%'].plot(kind='bar', ax=axes[0,0], color='steelblue')
axes[0,0].set_title('Market Share by Borough (%)')
axes[0,0].set_ylabel('Market Share (%)')
axes[0,0].tick_params(axis='x', rotation=45)
axes[0,0].grid(True, alpha=0.3)

# 2. Average price by borough
borough_analysis['Avg_Price'].plot(kind='bar', ax=axes[0,1], color='coral')
axes[0,1].set_title('Average Price by Borough')
axes[0,1].set_ylabel('Average Price ($)')
axes[0,1].tick_params(axis='x', rotation=45)
axes[0,1].grid(True, alpha=0.3)

# 3. Total listings by borough
borough_analysis['Total_Listings'].plot(kind='bar', ax=axes[1,0], color='lightgreen')
axes[1,0].set_title('Total Listings by Borough')
axes[1,0].set_ylabel('Number of Listings')
axes[1,0].tick_params(axis='x', rotation=45)
axes[1,0].grid(True, alpha=0.3)

# 4. Review rate by borough
borough_analysis['Review_Rate_%'].plot(kind='bar', ax=axes[1,1], color='gold')
axes[1,1].set_title('Review Rate by Borough (%)')
axes[1,1].set_ylabel('Review Rate (%)')
axes[1,1].tick_params(axis='x', rotation=45)
axes[1,1].grid(True, alpha=0.3)

fig.suptitle('Geographic Market Analysis by Borough', fontsize=16, fontweight='bold')
plt.tight_layout()
plt.show()

# Temporal analysis of review activity
review_data = df.dropna(subset=['last_review']).copy()
review_data['review_month'] = review_data['last_review'].dt.to_period('M')
review_data['review_year'] = review_data['last_review'].dt.year

# Monthly review activity
monthly_reviews = review_data.groupby('review_month').size()

# Create temporal visualizations
fig, axes = plt.subplots(2, 2, figsize=(16, 10))

# 1. Monthly review activity trend
monthly_reviews.plot(ax=axes[0,0], color='navy', linewidth=2)
axes[0,0].set_title('Monthly Review Activity (2019)')
axes[0,0].set_xlabel('Month')
axes[0,0].set_ylabel('Number of Reviews')
axes[0,0].grid(True, alpha=0.3)
axes[0,0].tick_params(axis='x', rotation=45)

# 2. Review activity by room type over time
room_time = review_data.groupby(['review_month', 'room_type']).size().unstack(fill_value=0)
room_time.plot(ax=axes[0,1], kind='area', stacked=True, alpha=0.7)
axes[0,1].set_title('Review Activity by Room Type Over Time')
axes[0,1].set_xlabel('Month')
axes[0,1].set_ylabel('Number of Reviews')
axes[0,1].legend(title='Room Type', bbox_to_anchor=(1.05, 1), loc='upper left')
axes[0,1].tick_params(axis='x', rotation=45)

# 3. Average price trend over time
price_trend = review_data.groupby('review_month')['price'].mean()
price_trend.plot(ax=axes[1,0], color='green', linewidth=2, marker='o')
axes[1,0].set_title('Average Price Trend Over Time')
axes[1,0].set_xlabel('Month')
axes[1,0].set_ylabel('Average Price ($)')
axes[1,0].grid(True, alpha=0.3)
axes[1,0].tick_params(axis='x', rotation=45)

# 4. Seasonal patterns (day of week for last review)
review_data['day_of_week'] = review_data['last_review'].dt.day_name()
day_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
day_reviews = review_data['day_of_week'].value_counts().reindex(day_order)
day_reviews.plot(kind='bar', ax=axes[1,1], color='purple', alpha=0.7)
axes[1,1].set_title('Review Activity by Day of Week')
axes[1,1].set_xlabel('Day of Week')
axes[1,1].set_ylabel('Number of Reviews')
axes[1,1].tick_params(axis='x', rotation=45)
axes[1,1].grid(True, alpha=0.3)

fig.suptitle('Temporal Patterns in Airbnb Market Activity', fontsize=16, fontweight='bold', x=0.3, horizontalalignment='left')
plt.tight_layout()
plt.show()

# Print temporal insights
peak_month = monthly_reviews.idxmax()
peak_reviews = monthly_reviews.max()
print(f"\n📈 TEMPORAL INSIGHTS:")
print(f"   • Peak review month: {peak_month} ({peak_reviews:,} reviews)")
print(f"   • Average monthly reviews: {monthly_reviews.mean():.0f}")
print(f"   • Most active day: {day_reviews.idxmax()} ({day_reviews.max():,} reviews)")

# Host analysis
host_data = df.dropna(subset=['host_name'])
host_stats = host_data.groupby('host_name').agg({
    'listing_id': 'count',
    'price': 'mean',
    'room_type': lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else 'Unknown'
}).round(2)

host_stats.columns = ['Total_Listings', 'Avg_Price', 'Primary_Room_Type']
host_stats = host_stats.sort_values('Total_Listings', ascending=False)

# Multi-listing hosts analysis
multi_listing_hosts = host_stats[host_stats['Total_Listings'] > 1]
single_listing_hosts = host_stats[host_stats['Total_Listings'] == 1]

print("👥 HOST ANALYSIS")
print("=" * 25)
print(f"📊 Total unique hosts: {len(host_stats):,}")
print(f"🏠 Single-listing hosts: {len(single_listing_hosts):,} ({len(single_listing_hosts)/len(host_stats)*100:.1f}%)")
print(f"🏢 Multi-listing hosts: {len(multi_listing_hosts):,} ({len(multi_listing_hosts)/len(host_stats)*100:.1f}%)")
print(f"📈 Average listings per host: {host_stats['Total_Listings'].mean():.2f}")

print(f"\n🔝 TOP 10 HOSTS BY LISTING COUNT:")
print(host_stats.head(10))

# Visualizations
fig, axes = plt.subplots(2, 2, figsize=(16, 10))
fig.suptitle('Host Analysis and Market Concentration', fontsize=16, fontweight='bold')

# 1. Distribution of listings per host
listing_distribution = host_stats['Total_Listings'].value_counts().sort_index()
listing_distribution.head(20).plot(kind='bar', ax=axes[0,0], color='teal')
axes[0,0].set_title('Distribution of Listings per Host')
axes[0,0].set_xlabel('Number of Listings')
axes[0,0].set_ylabel('Number of Hosts')
axes[0,0].grid(True, alpha=0.3)

# 2. Market concentration (top hosts)
top_hosts = host_stats.head(20)['Total_Listings']
top_hosts.plot(kind='bar', ax=axes[0,1], color='orange')
axes[0,1].set_title('Top 20 Hosts by Listing Count')
axes[0,1].set_xlabel('Host Rank')
axes[0,1].set_ylabel('Number of Listings')
axes[0,1].tick_params(axis='x', rotation=45)
axes[0,1].grid(True, alpha=0.3)

# 3. Single vs Multi-listing hosts
host_categories = ['Single Listing', 'Multiple Listings']
host_counts = [len(single_listing_hosts), len(multi_listing_hosts)]
axes[1,0].pie(host_counts, labels=host_categories, autopct='%1.1f%%', 
              colors=['lightblue', 'lightcoral'], startangle=90)
axes[1,0].set_title('Host Distribution: Single vs Multi-listing')

# 4. Average price by number of listings (for hosts with 1-10 listings)
price_by_listings = host_stats[host_stats['Total_Listings'] <= 10].groupby('Total_Listings')['Avg_Price'].mean()
price_by_listings.plot(kind='line', ax=axes[1,1], marker='o', color='red', linewidth=2)
axes[1,1].set_title('Average Price vs Number of Listings per Host')
axes[1,1].set_xlabel('Number of Listings per Host')
axes[1,1].set_ylabel('Average Price ($)')
axes[1,1].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Market concentration metrics
total_listings = host_stats['Total_Listings'].sum()
top_10_share = host_stats.head(10)['Total_Listings'].sum() / total_listings * 100
top_50_share = host_stats.head(50)['Total_Listings'].sum() / total_listings * 100

print(f"\n📊 MARKET CONCENTRATION:")
print(f"   • Top 10 hosts control: {top_10_share:.1f}% of listings")
print(f"   • Top 50 hosts control: {top_50_share:.1f}% of listings")
print(f"   • Largest host has: {host_stats.iloc[0]['Total_Listings']} listings")

# Statistical analysis and hypothesis testing
print("📊 STATISTICAL ANALYSIS")
print("=" * 30)

# 1. Test if there's a significant difference in prices between room types
private_prices = df[df['room_type'] == 'private room']['price']
entire_prices = df[df['room_type'] == 'entire homes/apartments']['price']
shared_prices = df[df['room_type'] == 'shared rooms']['price']

# Perform ANOVA test
f_stat, p_value = stats.f_oneway(private_prices, entire_prices, shared_prices)

print(f"\n🧪 ANOVA Test - Price differences between room types:")
print(f"   • F-statistic: {f_stat:.2f}")
print(f"   • P-value: {p_value:.2e}")
print(f"   • Result: {'Significant' if p_value < 0.05 else 'Not significant'} difference (α = 0.05)")

# 2. Correlation analysis
# Create numerical features for correlation
df_corr = df.copy()
df_corr['room_type_encoded'] = df_corr['room_type'].cat.codes
df_corr['has_review'] = df_corr['last_review'].notna().astype(int)
df_corr['days_since_review'] = (pd.Timestamp.now() - df_corr['last_review']).dt.days

# Calculate correlations
corr_features = ['price', 'room_type_encoded', 'has_review']
correlation_matrix = df_corr[corr_features].corr()

print(f"\n🔗 CORRELATION ANALYSIS:")
print(correlation_matrix.round(3))

# 3. Price distribution normality test
shapiro_stat, shapiro_p = stats.shapiro(df['price'].sample(5000))  # Sample for computational efficiency
print(f"\n📈 NORMALITY TEST (Shapiro-Wilk on sample):")
print(f"   • Statistic: {shapiro_stat:.4f}")
print(f"   • P-value: {shapiro_p:.2e}")
print(f"   • Distribution: {'Normal' if shapiro_p > 0.05 else 'Not normal'} (α = 0.05)")

# 4. Confidence interval for mean price
confidence_level = 0.95
degrees_freedom = len(df) - 1
sample_mean = df['price'].mean()
sample_std = df['price'].std()
standard_error = sample_std / np.sqrt(len(df))
margin_error = stats.t.ppf((1 + confidence_level) / 2, degrees_freedom) * standard_error

ci_lower = sample_mean - margin_error
ci_upper = sample_mean + margin_error

print(f"\n📊 95% CONFIDENCE INTERVAL FOR MEAN PRICE:")
print(f"   • Mean: ${sample_mean:.2f}")
print(f"   • 95% CI: [${ci_lower:.2f}, ${ci_upper:.2f}]")
print(f"   • Margin of error: ±${margin_error:.2f}")

# Generate comprehensive business insights
print("💼 BUSINESS INSIGHTS & RECOMMENDATIONS")
print("=" * 50)

# Key metrics summary
total_revenue_potential = df['price'].sum()
avg_occupancy_estimate = 0.7  # Industry estimate
estimated_annual_revenue = total_revenue_potential * 365 * avg_occupancy_estimate

print(f"\n📈 MARKET OVERVIEW:")
print(f"   • Total listings analyzed: {len(df):,}")
print(f"   • Market span: {(last_reviewed - first_reviewed).days} days")
print(f"   • Average nightly rate: ${avg_price}")
print(f"   • Estimated market size: ${estimated_annual_revenue/1e9:.1f}B annually")

print(f"\n🏆 TOP PERFORMING SEGMENTS:")
print(f"   • Highest revenue borough: {borough_prices.index[0]}")
print(f"   • Most popular room type: {room_distribution.index[0]} ({room_percentages.iloc[0]:.1f}%)")
print(f"   • Peak activity month: {peak_month}")

print(f"\n💡 KEY INSIGHTS:")
print(f"   1. Market Concentration: Top 10 hosts control {top_10_share:.1f}% of listings")
print(f"   2. Price Variation: {((borough_prices.max() - borough_prices.min()) / borough_prices.mean() * 100):.0f}% price difference across boroughs")
print(f"   3. Room Type Strategy: Entire homes command {(price_by_room.loc['entire homes/apartments', 'mean'] / avg_price - 1) * 100:.0f}% premium")
print(f"   4. Review Activity: {(len(review_data)/len(df)*100):.1f}% of listings have reviews")

print(f"\n🎯 STRATEGIC RECOMMENDATIONS:")
print(f"   1. 📍 LOCATION STRATEGY:")
print(f"      • Focus on {borough_prices.index[0]} for premium pricing")
print(f"      • Consider {borough_prices.index[-1]} for volume strategy")
print(f"   2. 🏠 PROPERTY TYPE:")
print(f"      • Entire homes/apartments offer highest revenue potential")
print(f"      • Private rooms provide good volume opportunity")
print(f"   3. 💰 PRICING OPTIMIZATION:")
print(f"      • Consider dynamic pricing based on borough and room type")
print(f"      • Monitor competitor pricing in high-value areas")
print(f"   4. 📊 MARKET ENTRY:")
print(f"      • Opportunity exists for new hosts (market not oversaturated)")
print(f"      • Focus on underserved neighborhoods for competitive advantage")

print(f"\n⚠️  RISK FACTORS:")
print(f"   • High market concentration among top hosts")
print(f"   • Significant price volatility (std dev: ${df['price'].std():.0f})")
print(f"   • {((len(df) - len(review_data))/len(df)*100):.1f}% of listings lack recent reviews")